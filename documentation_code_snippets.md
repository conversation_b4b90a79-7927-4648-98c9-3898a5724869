# Legend Fitness Club Django Gym Management System
## Code Documentation - Key Snippets for Technical Reference

---

## 1. MEMBERS APP

### File: `members/models.py` - Core Member and Package Models

```python
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from user.models import User

class Package(models.Model):
    """
    Model for membership packages with bilingual support
    """
    DURATION_CHOICES = [
        (1, _('1 Month')),
        (3, _('3 Months')),
        (6, _('6 Months')),
        (12, _('12 Months')),
    ]
    
    package_id = models.CharField(max_length=20, unique=True, editable=False)
    name_en = models.CharField(max_length=100, verbose_name=_("Package Name (English)"))
    name_km = models.CharField(max_length=100, verbose_name=_("Package Name (Khmer)"))
    duration = models.IntegerField(choices=DURATION_CHOICES, verbose_name=_("Duration (Months)"))
    price = models.IntegerField(verbose_name=_("Price (KHR)"))
    description_en = models.TextField(blank=True, verbose_name=_("Description (English)"))
    description_km = models.TextField(blank=True, verbose_name=_("Description (Khmer)"))
    is_active = models.BooleanField(default=True, verbose_name=_("Is Active"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.package_id:
            # Auto-generate package ID
            last_package = Package.objects.order_by('-id').first()
            if last_package:
                last_id = int(last_package.package_id.replace('PKG', ''))
                new_id = last_id + 1
            else:
                new_id = 1
            self.package_id = f'PKG{new_id:04d}'
        super().save(*args, **kwargs)

    def get_localized_name(self, language='en'):
        """Return package name in specified language"""
        return self.name_km if language == 'km' else self.name_en

    class Meta:
        ordering = ['duration', 'price']
        verbose_name = _("Package")
        verbose_name_plural = _("Packages")

class Member(models.Model):
    """
    Core member model with membership tracking and financial integration
    """
    GENDER_CHOICES = [
        ('male', _('Male')),
        ('female', _('Female')),
        ('other', _('Other')),
    ]
    
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('expired', _('Expired')),
        ('suspended', _('Suspended')),
    ]

    # Personal Information
    member_id = models.CharField(max_length=20, unique=True, editable=False)
    first_name = models.CharField(max_length=50, verbose_name=_("First Name"))
    last_name = models.CharField(max_length=50, verbose_name=_("Last Name"))
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, verbose_name=_("Gender"))
    date_of_birth = models.DateField(verbose_name=_("Date of Birth"))
    phone = models.CharField(max_length=20, verbose_name=_("Phone Number"))
    email = models.EmailField(blank=True, verbose_name=_("Email"))
    address = models.TextField(verbose_name=_("Address"))
    
    # Membership Information
    package = models.ForeignKey(Package, on_delete=models.CASCADE, verbose_name=_("Package"))
    start_date = models.DateField(verbose_name=_("Start Date"))
    end_date = models.DateField(verbose_name=_("End Date"))
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    # System Fields
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("Created By"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.member_id:
            # Auto-generate member ID
            last_member = Member.objects.order_by('-id').first()
            if last_member:
                last_id = int(last_member.member_id.replace('MEM', ''))
                new_id = last_id + 1
            else:
                new_id = 1
            self.member_id = f'MEM{new_id:04d}'
        
        # Auto-calculate end date based on package duration
        if self.start_date and self.package:
            self.end_date = self.start_date + timedelta(days=self.package.duration * 30)
        
        super().save(*args, **kwargs)

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def is_expired(self):
        return self.end_date < timezone.now().date()

    @property
    def days_remaining(self):
        if self.is_expired:
            return 0
        return (self.end_date - timezone.now().date()).days

    def get_membership_status_display_class(self):
        """Return CSS class for membership status"""
        status_classes = {
            'active': 'badge-success',
            'inactive': 'badge-secondary',
            'expired': 'badge-danger',
            'suspended': 'badge-warning'
        }
        return status_classes.get(self.status, 'badge-secondary')

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Member")
        verbose_name_plural = _("Members")
```

### File: `members/views.py` - Member Management with Permissions

```python
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.db.models import Q
from django.core.paginator import Paginator
from core.decorators import module_permission_required
from core.logging_utils import log_create_action, log_edit_action, log_delete_action
from .models import Member, Package

@login_required
@module_permission_required(module='member', required_level='view')
def index(request):
    """
    Main member listing with search, filtering, and pagination
    """
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    package_filter = request.GET.get('package', '')
    
    # Base queryset
    members = Member.objects.select_related('package', 'created_by').all()
    
    # Apply search filter
    if search_query:
        members = members.filter(
            Q(member_id__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(phone__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    # Apply status filter
    if status_filter:
        members = members.filter(status=status_filter)
    
    # Apply package filter
    if package_filter:
        members = members.filter(package_id=package_filter)
    
    # Pagination
    paginator = Paginator(members, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get packages for filter dropdown
    packages = Package.objects.filter(is_active=True)
    
    context = {
        'page_obj': page_obj,
        'packages': packages,
        'search_query': search_query,
        'status_filter': status_filter,
        'package_filter': package_filter,
        'total_members': members.count(),
    }
    
    return render(request, 'members/index.html', context)

@login_required
@module_permission_required(module='member', required_level='edit')
def create_member(request):
    """
    Create new member with automatic ID generation and membership calculation
    """
    if request.method == 'POST':
        try:
            # Extract form data
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            gender = request.POST.get('gender')
            date_of_birth = request.POST.get('date_of_birth')
            phone = request.POST.get('phone')
            email = request.POST.get('email', '')
            address = request.POST.get('address')
            package_id = request.POST.get('package')
            start_date = request.POST.get('start_date')
            
            # Validate required fields
            if not all([first_name, last_name, gender, date_of_birth, phone, address, package_id, start_date]):
                messages.error(request, _('All required fields must be filled.'))
                return redirect('member:create_member')
            
            # Get package
            package = get_object_or_404(Package, id=package_id, is_active=True)
            
            # Create member
            member = Member.objects.create(
                first_name=first_name,
                last_name=last_name,
                gender=gender,
                date_of_birth=date_of_birth,
                phone=phone,
                email=email,
                address=address,
                package=package,
                start_date=start_date,
                created_by=request.user
            )
            
            # Log the action
            log_create_action(request.user, 'member', member.id, f"Created member: {member.full_name}")
            
            messages.success(request, _('Member created successfully.'))
            return redirect('member:index')
            
        except Exception as e:
            messages.error(request, _('Error creating member: {}').format(str(e)))
            return redirect('member:create_member')
    
    # GET request - show form
    packages = Package.objects.filter(is_active=True)
    context = {
        'packages': packages,
    }
    
    return render(request, 'members/create.html', context)
```
