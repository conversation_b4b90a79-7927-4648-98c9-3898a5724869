# System Analysis Documentation - Legend Fitness Club

## 4.2 System Analysis (ការវិភាគប្រព័ន្ធ)

This section provides comprehensive system analysis for the Legend Fitness Club gym management system, including use case analysis, context diagrams, data flow analysis, and entity relationship modeling.

### 4.2.1 Use Case Diagram Analysis (៤.២.១ ការវិភាគដ្យាក្រាមករណីប្រើប្រាស់)

The use case diagram illustrates the primary interactions between different actors and the gym management system, showing role-based access control and core functionalities.

```mermaid
graph TB
    subgraph "Legend Fitness Club System"
        UC1[Member Registration]
        UC2[Payment Processing]
        UC3[POS Sales]
        UC4[Financial Management]
        UC5[Employee Management]
        UC6[Pay-per-visit]
        UC7[Report Generation]
        UC8[System Settings]
    end
    
    Admin[👤 Admin]
    Coach[👤 Coach]
    Cashier[👤 Cashier]
    Member[👤 Member]
    Supplier[👤 Supplier]
    
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC7
    Admin --> UC8
    
    Coach --> UC1
    Coach --> UC2
    Coach --> UC6
    
    Cashier --> UC2
    Cashier --> UC3
    Cashier --> UC6
    
    Member -.-> UC1
    Supplier -.-> UC3
```

**Use Case Analysis Summary:**
The Legend Fitness Club system supports five primary user roles with hierarchical permission levels. Admin users have full system access including employee management, financial oversight, and system configuration. Coaches focus on member management and training services, while Cashiers handle daily transactions through POS and pay-per-visit modules. The permission-based architecture ensures secure access control with four levels: No Access, View Only, View and Edit, and Full Access. This design supports the gym's operational workflow while maintaining data security and role separation.

### 4.2.2 Context Diagram Analysis (៤.២.២ ការវិភាគដ្យាក្រាមបរិបទ)

The context diagram shows the Legend Fitness Club system boundaries and its interactions with external entities in the Cambodian business environment.

```mermaid
graph TB
    subgraph "External Environment"
        Members[🏃‍♂️ Gym Members]
        Staff[👥 Staff Roles<br/>Admin, Coach, Cashier, Cleaner, Security]
        Suppliers[🏪 Local Suppliers<br/>Equipment & Products]
        Financial[💰 Financial Systems<br/>KHR & USD Transactions]
        Reports[📊 Management Reports<br/>Business Intelligence]
    end
    
    subgraph "Legend Fitness Club System"
        GymSystem[🏋️‍♀️ Gym Management<br/>System Core]
    end
    
    Members <--> GymSystem
    Staff <--> GymSystem
    Suppliers <--> GymSystem
    Financial <--> GymSystem
    Reports <--> GymSystem
```

**Context Analysis Summary:**
The gym management system operates as a centralized hub processing data flows from five key external entities. Members interact through registration and payment processes, while staff members access role-specific functionalities based on their permission levels. Local suppliers integrate through purchase management for gym equipment and retail products. The financial system handles dual-currency operations (Cambodian Riel and USD) with automatic conversion capabilities. Management reports provide business intelligence for operational decision-making, supporting the gym's growth in the competitive Cambodian fitness market.

### 4.2.3 Data Flow Diagram Analysis (៤.២.៣ ការវិភាគដ្យាក្រាមលំហូរទិន្នន័យ)

The Level 0 Data Flow Diagram illustrates the main processes and data movements within the gym management system.

```mermaid
graph LR
    subgraph "Data Stores"
        DS1[(Member Database)]
        DS2[(Product Database)]
        DS3[(Financial Database)]
        DS4[(User Database)]
        DS5[(Transaction Logs)]
    end
    
    subgraph "Main Processes"
        P1[1.0<br/>Member<br/>Management]
        P2[2.0<br/>Payment<br/>Processing]
        P3[3.0<br/>POS<br/>Operations]
        P4[4.0<br/>Financial<br/>Management]
        P5[5.0<br/>Employee<br/>Management]
        P6[6.0<br/>Reporting<br/>System]
    end
    
    Members[Members] --> P1
    Staff[Staff] --> P1
    P1 --> DS1
    
    P1 --> P2
    P2 --> DS3
    P2 --> DS5
    
    Cashier[Cashier] --> P3
    P3 --> DS2
    P3 --> DS3
    
    Admin[Admin] --> P4
    P4 --> DS3
    
    Admin --> P5
    P5 --> DS4
    
    P2 --> P6
    P3 --> P6
    P4 --> P6
    P6 --> Reports[Reports]
```

**Data Flow Analysis Summary:**
The system processes data through six interconnected modules with centralized data storage. Member Management (1.0) handles registration and package assignments, feeding data to Payment Processing (2.0) for financial transactions. POS Operations (3.0) manages retail sales and inventory updates, while Financial Management (4.0) oversees deposits, withdrawals, and bill payments. Employee Management (5.0) controls user access and payroll, with all processes contributing to the Reporting System (6.0). Data flows maintain referential integrity through foreign key relationships, ensuring consistent information across all modules while supporting real-time transaction processing.

### 4.2.4 Entity Relationship Diagram Analysis (៤.២.៤ ការវិភាគដ្យាក្រាមទំនាក់ទំនងអង្គភាព)

The ERD shows the core entities and their relationships within the gym management database structure.

```mermaid
erDiagram
    User {
        int id PK
        string username UK
        string name
        string role
        string phone UK
        boolean is_employee
        int salary
        datetime created_at
    }
    
    Member {
        int id PK
        string member_id UK
        string name
        string gender
        date dob
        string contact
        int package_id FK
        date start_date
        date end_date
        string payment_status
        int discount
        int due_payment
    }
    
    Package {
        int id PK
        string package_id UK
        string name
        int duration
        int price_khr
        decimal price_usd
        string access_type
    }
    
    Payment {
        int id PK
        string invoice_no UK
        int member_id FK
        int amount_khr
        decimal amount_usd
        string payment_method
        int collector_id FK
        datetime payment_date
    }
    
    Product {
        int id PK
        string sku UK
        string name
        int category_id FK
        int cost_price
        int retail_price
        int quantity
        int box_quantity
        int box_cost
        boolean is_active
    }
    
    Sale {
        int id PK
        string trxId UK
        int total_amount
        string payment_method
        int sold_by_id FK
        datetime date
    }
    
    FinanceTransaction {
        int id PK
        string transaction_id UK
        string transaction_type
        int amount_khr
        decimal amount_usd
        string payment_method
        int staff_id FK
        datetime created_at
    }
    
    RolePermission {
        int id PK
        string role
        string module
        string permission_level
    }
    
    User ||--o{ Member : "manages"
    Member }o--|| Package : "subscribes_to"
    Member ||--o{ Payment : "makes"
    User ||--o{ Payment : "collects"
    User ||--o{ Sale : "processes"
    Product ||--o{ Sale : "sold_in"
    User ||--o{ FinanceTransaction : "creates"
    User }o--|| RolePermission : "has_permissions"
```

**Entity Relationship Analysis Summary:**
The database structure supports comprehensive gym operations through eight core entities with well-defined relationships. The User entity serves as the central authentication and authorization hub, linking to Members through management relationships and to financial transactions through collector/processor roles. Members connect to Packages for subscription management and to Payments for financial tracking. The Product-Sale relationship enables POS functionality, while FinanceTransaction handles deposits and withdrawals. The RolePermission entity implements the four-level access control system (No Access, View Only, View and Edit, Full Access) across all modules. Currency support includes both Cambodian Riel (integer) and USD (decimal) fields with automatic conversion capabilities, ensuring accurate financial reporting for the Cambodian market.

---

## Technical Implementation Notes

**Permission System:** The system implements role-based access control through the RolePermission model with 13 modules (dashboard, member, payment, payroll, product, purchase, pos, paypervisit, bill, finance, financialreport, user, settings) and 4 permission levels.

**Currency Handling:** All financial amounts use integer fields for KHR (Cambodian Riel) with thousand separators and decimal fields for USD, supporting automatic conversion based on system exchange rates.

**Data Integrity:** Foreign key relationships ensure referential integrity across all modules, with CASCADE and SET_NULL options appropriately configured for business logic requirements.
